# 金币提现页面图片资源清单

## 📋 需要添加的图片资源

### 1. 导航栏图标
```
nav_more_dots.png
<EMAIL>  
<EMAIL>
```
- **用途**：右上角更多按钮
- **建议尺寸**：24x24pt (实际像素：24x24, 48x48, 72x72)
- **样式**：三个点的图标，颜色建议为 #666666

### 2. 提现说明图标
```
withdrawal_info_icon.png
<EMAIL>
<EMAIL>
```
- **用途**：可提现金币区域的说明按钮
- **建议尺寸**：16x16pt (实际像素：16x16, 32x32, 48x48)
- **样式**：问号或信息图标，颜色建议为 #999999

### 3. 支付方式图标
```
withdrawal_alipay_icon.png
<EMAIL>
<EMAIL>

withdrawal_wechat_icon.png
<EMAIL>
<EMAIL>
```
- **用途**：提现账户选择区域
- **建议尺寸**：20x20pt (实际像素：20x20, 40x40, 60x60)
- **样式**：
  - 支付宝：蓝色方形图标，白色"支"字
  - 微信：绿色圆形图标，白色对话框

### 4. 箭头图标
```
arrow_right_gray.png
<EMAIL>
<EMAIL>
```
- **用途**：提现账户管理按钮、查看全部记录按钮
- **建议尺寸**：16x16pt (实际像素：16x16, 32x32, 48x48)
- **样式**：右箭头，颜色为 #CCCCCC

## 🎨 设计规范

### 颜色规范
- **主题橙色**：#FF8D36
- **深灰色文字**：#333333
- **中灰色文字**：#666666
- **浅灰色文字**：#999999
- **分割线颜色**：#E5E5E5

### 图标设计要求
1. **风格统一**：所有图标应保持一致的设计风格
2. **清晰度**：确保在不同分辨率下都清晰可见
3. **适配性**：图标应适配浅色和深色背景
4. **品牌一致性**：支付宝和微信图标应使用官方标准颜色

## 📱 使用位置说明

### 导航栏
- `nav_more_dots.png`：右上角更多按钮

### 可提现金币卡片
- `withdrawal_info_icon.png`：标题右侧的说明按钮

### 提现账户卡片  
- `withdrawal_alipay_icon.png`：支付宝选项图标
- `withdrawal_wechat_icon.png`：微信支付选项图标
- `arrow_right_gray.png`：标题右侧的管理按钮

### 变动记录区域
- `arrow_right_gray.png`：查看全部按钮右侧箭头

## 🔧 代码中的使用

```swift
// 导航栏更多按钮
moreButton.setImage(UIImage(named: "nav_more_dots"), for: .normal)

// 提现说明按钮  
withdrawalInfoButton.setImage(UIImage(named: "withdrawal_info_icon"), for: .normal)

// 支付宝图标
iconImageView.image = UIImage(named: "withdrawal_alipay_icon")

// 微信图标
iconImageView.image = UIImage(named: "withdrawal_wechat_icon")

// 右箭头图标
button.setImage(UIImage(named: "arrow_right_gray"), for: .normal)
```

## ✅ 添加步骤

1. 将图片文件拖拽到 Xcode 项目的 Assets.xcassets 中
2. 确保图片命名与上述清单一致
3. 检查图片在不同设备上的显示效果
4. 如需调整图片大小或颜色，请相应修改代码中的约束或颜色设置

添加完这些图片资源后，金币提现页面将完全符合设计稿的视觉效果！
